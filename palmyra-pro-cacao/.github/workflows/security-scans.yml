# Reusable workflow for security vulnerability scanning
# This workflow performs dependency and container security scans using Trivy
#
# Required inputs:
#   - tag-name: Docker image tag to scan
#   - gcp-project-id: GCP Project ID for container registry access
#   - gcp-region: GCP Region for container registry
#
# Required secrets:
#   - GCP_SERVICE_ACCOUNT_KEY: Service account credentials for GCP authentication
#
# Features:
#   - Dependency vulnerability scanning (filesystem)
#   - Container image vulnerability scanning
#   - Critical vulnerabilities block pipeline (configurable via PR labels)
#   - Comprehensive reporting (table + JSON artifacts)

name: Security Scans
run-name: Security Scans for ${{ inputs.tag-name || github.sha }}

on:
  workflow_call:
    inputs:
      tag-name:
        description: "Docker image tag to scan"
        required: true
        type: string
      gcp-project-id:
        description: "GCP Project ID"
        required: true
        type: string
      gcp-region:
        description: "GCP Region"
        required: true
        type: string
      run-scans:
        description: "Whether to run security scans"
        required: false
        type: boolean
        default: true
      environment:
        description: "GitHub environment to use"
        required: false
        type: string
        default: "development"
    secrets:
      GCP_SERVICE_ACCOUNT_KEY:
        description: "GCP Service Account Key for authentication"
        required: true

jobs:
  security-scan:
    name: "Security Vulnerability Scans"
    runs-on: ubuntu-latest
    if: inputs.run-scans == true
    environment: ${{ inputs.environment }}
    env:
      TAG_NAME: ${{ inputs.tag-name }}
      GCP_PROJECT_ID: ${{ inputs.gcp-project-id }}
      GCP_REGION: ${{ inputs.gcp-region }}
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Debug Environment Variables
        run: |
          echo "=== Environment Variables ==="
          echo "TAG_NAME: ${{ env.TAG_NAME }}"
          echo "GCP_PROJECT_ID: ${{ env.GCP_PROJECT_ID }}"
          echo "GCP_REGION: ${{ env.GCP_REGION }}"
          echo ""
          echo "=== Inputs ==="
          echo "tag-name: '${{ inputs.tag-name }}'"
          echo "gcp-project-id: '${{ inputs.gcp-project-id }}'"
          echo "gcp-region: '${{ inputs.gcp-region }}'"
          echo "run-scans: ${{ inputs.run-scans }}"

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Configure Docker
        run: gcloud auth configure-docker "${{ inputs.gcp-region }}-docker.pkg.dev"

      - name: Dependency Security Scan
        uses: aquasecurity/trivy-action@0.32.0
        continue-on-error: ${{ contains(github.event.pull_request.labels.*.name, 'bypass-vulnerability-checks') }}
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'table'
          scanners: 'vuln,secret'
          severity: 'CRITICAL'
          exit-code: 1
          timeout: '10m'

      - name: Dependency Security Scan - JSON Report
        uses: aquasecurity/trivy-action@0.32.0
        continue-on-error: true
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'json'
          output: 'security-report.json'
          scanners: 'vuln,secret'
          severity: 'CRITICAL,HIGH,MEDIUM,LOW'
          exit-code: 0
          timeout: '10m'

      - name: Cacao API Container Security Scan
        uses: aquasecurity/trivy-action@0.32.0
        continue-on-error: ${{ contains(github.event.pull_request.labels.*.name, 'bypass-vulnerability-checks') }}
        with:
          image-ref: '${{ env.GCP_REGION }}-docker.pkg.dev/${{ env.GCP_PROJECT_ID }}/palmyra-pro-images/cacao-api:${{ env.TAG_NAME }}'
          format: 'table'
          scanners: 'vuln,secret'
          severity: 'CRITICAL'
          exit-code: 1
          timeout: '10m'

      - name: Cacao API Container Security Scan - JSON Report
        uses: aquasecurity/trivy-action@0.32.0
        continue-on-error: true
        with:
          image-ref: '${{ env.GCP_REGION }}-docker.pkg.dev/${{ env.GCP_PROJECT_ID }}/palmyra-pro-images/cacao-api:${{ env.TAG_NAME }}'
          format: 'json'
          output: 'cacao-api-container-report.json'
          scanners: 'vuln,secret'
          severity: 'CRITICAL,HIGH,MEDIUM,LOW'
          exit-code: 0
          timeout: '10m'

      - name: Upload Security Reports
        uses: actions/upload-artifact@v4
        with:
          name: security-reports-${{ env.TAG_NAME }}
          path: |
            security-report.json
            cacao-api-container-report.json
          retention-days: 30

      - name: Security Scan Summary
        run: |
          echo "🔒 Security scans completed for tag: ${{ env.TAG_NAME }}"
          echo "Reports uploaded as artifacts for detailed analysis"
